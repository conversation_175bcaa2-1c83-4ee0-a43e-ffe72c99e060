{"app": {"title": "MCP 互動回饋系統", "subtitle": "AI 助手互動回饋平台", "projectDirectory": "專案目錄"}, "tabs": {"feedback": "💬 回饋", "summary": "📋 AI 摘要", "commands": "⚡ 命令", "command": "⚡ 命令", "settings": "⚙️ 設定", "combined": "📝 合併模式", "about": "ℹ️ 關於"}, "feedback": {"title": "💬 提供回饋", "description": "請提供您對 AI 工作成果的回饋意見。您可以輸入文字回饋並上傳相關圖片。", "textLabel": "文字回饋", "placeholder": "請在這裡輸入您的回饋...", "detailedPlaceholder": "請在這裡輸入您的回饋...\n\n💡 小提示：\n• 按 Ctrl+Enter/Cmd+Enter (支援數字鍵盤) 可快速提交\n• 按 Ctrl+V/Cmd+V 可直接貼上剪貼板圖片", "imageLabel": "圖片附件（可選）", "imageUploadText": "📎 點擊選擇圖片或拖放圖片到此處\n支援 PNG、JPG、JPEG、GIF、BMP、WebP 等格式", "submit": "✅ 提交回饋", "uploading": "上傳中...", "dragdrop": "拖放圖片到這裡或點擊上傳", "selectfiles": "選擇檔案", "processing": "處理中...", "success": "回饋已成功提交！", "error": "提交回饋時發生錯誤", "shortcuts": {"submit": "Ctrl+Enter 提交 (Mac 用 Cmd+Enter，支援數字鍵盤)", "clear": "Ctrl+Delete 清除 (Mac 用 Cmd+Delete)", "paste": "Ctrl+V 貼上圖片 (Mac 用 Cmd+V)"}}, "summary": {"title": "📋 AI 工作摘要", "description": "以下是 AI 助手完成的工作摘要，請仔細查看並提供您的回饋意見。", "placeholder": "AI 工作摘要將在這裡顯示...", "empty": "目前沒有摘要內容", "lastupdate": "最後更新", "refresh": "重新整理"}, "commands": {"title": "⚡ 命令執行", "description": "在此執行命令來驗證結果或收集更多資訊。命令將在專案目錄中執行。", "inputLabel": "命令輸入", "placeholder": "輸入要執行的命令...", "execute": "▶️ 執行", "runButton": "▶️ 執行", "clear": "清除", "output": "命令輸出", "outputLabel": "命令輸出", "running": "執行中...", "completed": "執行完成", "error": "執行錯誤", "history": "命令歷史"}, "command": {"title": "⚡ 命令執行", "description": "在此執行命令來驗證結果或收集更多資訊。命令將在專案目錄中執行。", "inputLabel": "命令輸入", "placeholder": "輸入要執行的命令...", "execute": "▶️ 執行", "runButton": "▶️ 執行", "clear": "清除", "output": "命令輸出", "outputLabel": "命令輸出", "running": "執行中...", "completed": "執行完成", "error": "執行錯誤", "history": "命令歷史"}, "combined": {"description": "合併模式：AI 摘要和回饋輸入在同一頁面中，方便對照查看。", "summaryTitle": "📋 AI 工作摘要", "feedbackTitle": "💬 提供回饋"}, "settings": {"title": "⚙️ 設定", "description": "調整介面設定和偏好選項。", "language": "語言", "currentLanguage": "當前語言", "languageDesc": "選擇界面顯示語言", "interface": "介面設定", "layoutMode": "界面佈局模式", "layoutModeDesc": "選擇 AI 摘要和回饋輸入的顯示方式", "separateMode": "分離模式", "separateModeDesc": "AI 摘要和回饋分別在不同頁籤", "combinedVertical": "合併模式（垂直布局）", "combinedVerticalDesc": "AI 摘要在上，回饋輸入在下，摘要和回饋在同一頁面", "combinedHorizontal": "合併模式（水平布局）", "combinedHorizontalDesc": "AI 摘要在左，回饋輸入在右，增大摘要可視區域", "autoClose": "自動關閉頁面", "autoCloseDesc": "提交回饋後自動關閉頁面", "theme": "主題", "notifications": "通知", "advanced": "進階設定", "save": "儲存設定", "reset": "重置設定", "resetDesc": "清除所有已保存的設定，恢復到預設狀態", "resetConfirm": "確定要重置所有設定嗎？這將清除所有已保存的偏好設定。", "resetSuccess": "設定已重置為預設值", "resetError": "重置設定時發生錯誤", "timeout": "連線逾時 (秒)", "autorefresh": "自動重新整理", "debug": "除錯模式"}, "languages": {"zh-TW": "繁體中文", "zh-CN": "简体中文", "en": "English"}, "themes": {"dark": "深色", "light": "淺色", "auto": "自動"}, "status": {"connected": "已連線", "connecting": "連線中...", "disconnected": "已中斷連線", "reconnecting": "重新連線中...", "error": "連線錯誤"}, "notifications": {"feedback_sent": "回饋已發送", "command_executed": "指令已執行", "settings_saved": "設定已儲存", "connection_lost": "連線中斷", "connection_restored": "連線已恢復"}, "errors": {"connection_failed": "連線失敗", "upload_failed": "上傳失敗", "command_failed": "指令執行失敗", "invalid_input": "輸入內容無效", "timeout": "請求逾時"}, "buttons": {"ok": "確定", "cancel": "❌ 取消", "submit": "✅ 提交回饋", "retry": "重試", "close": "關閉", "upload": "上傳", "download": "下載"}, "session": {"timeout": "⏰ 會話已超時，介面將自動關閉", "timeoutWarning": "會話即將超時", "timeoutDescription": "由於長時間無回應，會話已超時。介面將在 3 秒後自動關閉。", "closing": "正在關閉..."}, "timeout": {"enable": "自動關閉", "enableTooltip": "啟用後將在指定時間後自動關閉介面", "duration": {"label": "超時時間", "description": "設置自動關閉的時間（30秒 - 2小時）"}, "seconds": "秒", "remaining": "剩餘時間", "expired": "⏰ 時間已到，介面將自動關閉", "autoCloseMessage": "介面將在 {seconds} 秒後自動關閉", "settings": {"title": "超時設置", "description": "啟用後，介面將在指定時間後自動關閉。倒數計時器會顯示在頂部區域。"}}, "dynamic": {"aiSummary": "測試 Web UI 功能\n\n🎯 **功能測試項目：**\n- Web UI 服務器啟動和運行\n- WebSocket 即時通訊\n- 回饋提交功能\n- 圖片上傳和預覽\n- 命令執行功能\n- 智能 Ctrl+V 圖片貼上\n- 多語言介面功能\n\n📋 **測試步驟：**\n1. 測試圖片上傳（拖拽、選擇檔案、剪貼簿）\n2. 在文字框內按 Ctrl+V 測試智能貼上\n3. 嘗試切換語言（繁中/簡中/英文）\n4. 測試命令執行功能\n5. 提交回饋和圖片\n\n請測試這些功能並提供回饋！", "terminalWelcome": "歡迎使用互動回饋終端\n========================================\n專案目錄: {sessionId}\n輸入命令後按 Enter 或點擊執行按鈕\n支援的命令: ls, dir, pwd, cat, type 等\n\n$ "}, "about": {"title": "ℹ️ 關於", "description": "一個強大的 MCP 伺服器，為 AI 輔助開發工具提供人在回路的互動回饋功能。支援 Qt GUI 和 Web UI 雙介面，並具備圖片上傳、命令執行、多語言等豐富功能。", "appInfo": "應用程式資訊", "version": "版本", "projectLinks": "專案連結", "githubProject": "GitHub 專案", "visitGithub": "訪問 GitHub", "contact": "聯繫與支援", "discordSupport": "Discord 支援", "joinDiscord": "加入 Discord", "contactDescription": "如需技術支援、問題回報或功能建議，歡迎透過 Discord 社群或 GitHub Issues 與我們聯繫。", "thanks": "致謝與貢獻", "thanksText": "感謝原作者 <PERSON><PERSON><PERSON> (@fabiomlferreira) 創建了原始的 interactive-feedback-mcp 專案。\n\n本增強版本由 Minidoracat 開發和維護，大幅擴展了專案功能，新增了 GUI 介面、圖片支援、多語言能力以及許多其他改進功能。\n\n同時感謝 sanshao85 的 mcp-feedback-collector 專案提供的 UI 設計靈感。\n\n開源協作讓技術變得更美好！"}, "images": {"settings": {"title": "圖片設定", "sizeLimit": "圖片大小限制", "sizeLimitOptions": {"unlimited": "無限制", "1mb": "1MB", "3mb": "3MB", "5mb": "5MB"}, "base64Detail": "Base64 相容模式", "base64DetailHelp": "啟用後會在文字中包含完整的 Base64 圖片資料，提升與某些 AI 模型的相容性", "base64Warning": "⚠️ 會增加傳輸量", "compatibilityHint": "💡 圖片無法正確識別？", "enableBase64Hint": "嘗試啟用 Base64 相容模式"}, "sizeLimitExceeded": "圖片 {filename} 大小為 {size}，超過 {limit} 限制！", "sizeLimitExceededAdvice": "建議使用圖片編輯軟體壓縮後再上傳，或調整圖片大小限制設定。"}}