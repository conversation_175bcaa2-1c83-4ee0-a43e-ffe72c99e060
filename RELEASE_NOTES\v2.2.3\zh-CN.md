# Release v2.2.3 - 超时控制与图片设置增强

## 🌟 亮点
本版本新增了用户可控制的超时设置功能，以及灵活的图片上传设置选项，同时完善了 UV Cache 管理工具，提升整体使用体验。

## ✨ 新功能
- ⏰ **用户超时控制**: 新增可自定义的超时设置功能，支持 30 秒至 2 小时的弹性设置
- ⏱️ **倒数计时器**: 界面顶部显示实时倒数计时器，提供可视化的时间提醒
- 🖼️ **图片大小限制**: 新增图片上传大小限制设置（无限制/1MB/3MB/5MB）
- 🔧 **Base64 兼容模式**: 新增 Base64 详细模式，提升部分 AI 模型的图片识别兼容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 脚本，协助管理和清理 UV cache 空间

## 🚀 改进功能
- 📚 **文档结构优化**: 重新整理文档目录结构，将图片移至 `docs/{语言}/images/` 路径
- 📖 **Cache 管理指南**: 新增详细的 UV Cache 管理指南，包含自动化清理方案
- 🎯 **智能兼容性提示**: 当图片上传失败时自动显示 Base64 兼容模式建议
- 🔄 **设置同步机制**: 改进图片设置在不同界面模式间的同步机制

## 🐛 问题修复
- 🛡️ **超时处理优化**: 改进用户自定义超时与 MCP 系统超时的协调机制
- 🖥️ **界面自动关闭**: 修复超时后界面自动关闭和资源清理逻辑
- 📱 **响应式布局**: 优化超时控制组件在小屏幕设备上的显示效果

## 🔧 技术改进
- 🎛️ **超时控制架构**: 实现前端倒数计时器与后端超时处理的分离设计
- 📊 **图片处理优化**: 改进图片上传的大小检查和格式验证机制
- 🗂️ **设置持久化**: 增强设置保存机制，确保用户偏好的正确保存和载入
- 🧰 **工具脚本增强**: 新增跨平台的 cache 清理工具，支持强制清理和预览模式

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.3 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 相关 PR: #22 (超时控制功能), #19 (图片设置功能)
