# Release v2.2.4 - GUI 體驗優化與問題修復

## 🌟 亮點
本版本專注於 GUI 使用體驗的優化，修復了圖片複製貼上的重複問題，重新組織了語系檔案結構，並改善了介面文字的可讀性。

## 🐛 問題修復
- 🖼️ **圖片重複貼上修復**: 解決 GUI 介面中使用 Ctrl+V 複製貼上圖片時出現重複貼上的問題
- 🌐 **語系切換修復**: 修復圖片設定區域在語言切換時文字沒有正確翻譯的問題
- 📝 **字體可讀性改善**: 調整圖片設定區域的字體大小，提升文字可讀性

## 📦 安裝與更新
```bash
# 快速測試最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.4 test
```

## 🔗 相關連結
- 完整文檔: [README.zh-TW.md](../../README.zh-TW.md)
- 問題回報: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 專案首頁: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced) 