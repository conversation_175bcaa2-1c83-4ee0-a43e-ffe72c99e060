# Release v2.2.1 - 窗口优化与统一设置接口

## 🌟 亮点
本版本主要解决了 GUI 窗口大小限制问题，实现了窗口状态的智能保存机制，并优化了设置接口的统一性。

## 🚀 改进功能
- 🖥️ **窗口大小限制解除**: 解除 GUI 主窗口最小大小限制，从 1000×800 降至 400×300，让用户可以自由调整窗口大小以符合不同使用场景
- 💾 **窗口状态实时保存**: 实现窗口大小与位置的即时保存机制，支持防抖延迟避免过度频繁的 I/O 操作
- ⚙️ **统一设置接口优化**: 改进 GUI 设置版面的配置保存逻辑，避免设置冲突，确保窗口定位与大小设置的正确性
- 🎯 **智能窗口大小保存**: 「总是在主屏幕中心显示」模式下正确保存窗口大小（但不保存位置），「智能定位」模式下保存完整的窗口状态

## 🐛 问题修复
- 🔧 **窗口大小限制**: 解决 GUI 窗口无法调整至小尺寸的问题 (fixes #10 第一部分)
- 🛡️ **设置冲突**: 修复设置保存时可能出现的配置冲突问题

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.1 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 解决问题: #10 (部分完成) 